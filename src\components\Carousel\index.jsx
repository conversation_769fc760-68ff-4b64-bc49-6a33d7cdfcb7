import { useState, useEffect, useCallback, useMemo } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function Carousel({
  items = [],
  autoScrollInterval = 5000,
  showArrows = true,
  showDots = true,
  responsive = null,
  renderItem,
  containerClassName = "",
  slideClassName = "",
  dotClassName = "",
  arrowClassName = "",
  activeDotClassName = "",
  inactiveDotClassName = "",
}) {
  const [current, setCurrent] = useState(1); // Start at 1 (first real slide)
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [screenSize, setScreenSize] = useState("lg");

  // Detect screen size for responsive behavior
  useEffect(() => {
    if (!responsive) return;

    const handleResize = () => {
      const width = window.innerWidth;
      if (width >= (responsive.lg?.breakpoint || 1024)) {
        setScreenSize("lg");
      } else if (width >= (responsive.md?.breakpoint || 768)) {
        setScreenSize("md");
      } else {
        setScreenSize("sm");
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [responsive]);

  // Memoize items per slide calculation
  const itemsPerSlide = useMemo(() => {
    if (!responsive) return 1;

    switch (screenSize) {
      case "lg":
        return responsive.lg?.itemsPerSlide || 1;
      case "md":
        return responsive.md?.itemsPerSlide || 1;
      default:
        return responsive.sm?.itemsPerSlide || 1;
    }
  }, [responsive, screenSize]);

  // Memoize grouped items to prevent unnecessary recalculations
  const groupedItems = useMemo(() => {
    const groups = [];

    if (responsive && itemsPerSlide > 1) {
      for (let i = 0; i < items.length; i += itemsPerSlide) {
        groups.push(items.slice(i, i + itemsPerSlide));
      }
    } else {
      // For non-responsive or single item carousels
      groups.push(...items.map((item) => [item]));
    }

    return groups;
  }, [items, responsive, itemsPerSlide]);

  // Memoize extended slides array
  const extendedSlides = useMemo(() => {
    if (groupedItems.length === 0) return [];

    return [
      groupedItems[groupedItems.length - 1],
      ...groupedItems,
      groupedItems[0],
    ];
  }, [groupedItems]);

  // Reset current when screen size changes
  useEffect(() => {
    if (responsive) {
      setCurrent(1);
    }
  }, [screenSize, responsive]);

  // Memoize transition end handler
  const handleTransitionEnd = useCallback(() => {
    setIsTransitioning(false);
    if (current === 0) {
      setCurrent(groupedItems.length);
    } else if (current === extendedSlides.length - 1) {
      setCurrent(1);
    }
  }, [current, groupedItems.length, extendedSlides.length]);

  const nextSlide = useCallback(() => {
    if (isTransitioning || groupedItems.length === 0) return;
    setIsTransitioning(true);
    setCurrent((prev) => prev + 1);
  }, [isTransitioning, groupedItems.length]);

  const prevSlide = useCallback(() => {
    if (isTransitioning || groupedItems.length === 0) return;
    setIsTransitioning(true);
    setCurrent((prev) => prev - 1);
  }, [isTransitioning, groupedItems.length]);

  const goToSlide = useCallback(
    (index) => {
      if (isTransitioning || groupedItems.length === 0) return;
      setIsTransitioning(true);
      setCurrent(index + 1);
    },
    [isTransitioning, groupedItems.length]
  );

  // Auto-scroll with proper cleanup
  useEffect(() => {
    if (!autoScrollInterval || groupedItems.length === 0) return;

    const interval = setInterval(() => {
      if (!isTransitioning) {
        setIsTransitioning(true);
        setCurrent((prev) => prev + 1);
      }
    }, autoScrollInterval);

    return () => clearInterval(interval);
  }, [autoScrollInterval, groupedItems.length, isTransitioning]);

  // Default render function for single items
  const defaultRenderItem = (item, index) => (
    <div key={index} className="w-full h-full">
      {typeof item === "string" ? (
        <img
          src={item}
          alt={`Slide ${index}`}
          className="w-full h-full object-cover"
        />
      ) : (
        <div>{JSON.stringify(item)}</div>
      )}
    </div>
  );

  const renderSlideContent = (slideItems, slideIndex) => {
    if (responsive && itemsPerSlide > 1) {
      // Multi-item responsive grid layout
      const gridCols =
        itemsPerSlide === 2
          ? "grid-cols-2"
          : itemsPerSlide === 3
          ? "grid-cols-3"
          : "grid-cols-4";

      return (
        <div className={`grid gap-4 sm:gap-6 ${gridCols}`}>
          {slideItems.map((item, itemIndex) => {
            return renderItem
              ? renderItem(item, itemIndex, itemsPerSlide)
              : defaultRenderItem(item, itemIndex);
          })}
        </div>
      );
    } else if (responsive && itemsPerSlide === 1) {
      // Single item responsive layout with width constraint
      return (
        <div className="flex justify-center">
          <div className="w-full max-w-2xs mx-auto">
            {renderItem
              ? renderItem(slideItems[0], slideIndex, itemsPerSlide)
              : defaultRenderItem(slideItems[0], slideIndex)}
          </div>
        </div>
      );
    } else {
      // Non-responsive single item layout
      return renderItem
        ? renderItem(slideItems[0], slideIndex)
        : defaultRenderItem(slideItems[0], slideIndex);
    }
  };
  return (
    <div className={`relative overflow-hidden ${containerClassName}`}>
      {/* Slides Container */}
      <div
        className={`flex h-full ${
          isTransitioning ? "transition-transform duration-800 ease-in-out" : ""
        }`}
        style={{ transform: `translateX(-${current * 100}%)` }}
        onTransitionEnd={handleTransitionEnd}
      >
        {extendedSlides.map((slideItems, slideIndex) => (
          <div
            key={slideIndex}
            className={`w-full flex-shrink-0 ${slideClassName}`}
          >
            {renderSlideContent(slideItems, slideIndex)}
          </div>
        ))}
      </div>

      {/* Navigation Arrows */}
      {showArrows && (
        <>
          <button
            className={`absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 z-40 ${arrowClassName}`}
            onClick={prevSlide}
          >
            <ChevronLeft className="w-6 h-6 text-white" />
          </button>
          <button
            className={`absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 z-40 ${arrowClassName}`}
            onClick={nextSlide}
          >
            <ChevronRight className="w-6 h-6 text-white" />
          </button>
        </>
      )}

      {/* Dots Navigation */}
      {showDots && (
        <div
          className={`flex justify-center mt-8 gap-2 sm:gap-3 ${dotClassName}`}
        >
          {groupedItems.map((_, i) => (
            <button
              key={i}
              onClick={() => goToSlide(i)}
              className={`w-2 h-1 md:w-3 md:h-1.5 lg:w-4 lg:h-2 rounded-full transition-all duration-300 hover:scale-125 ${
                i === (current - 1 + groupedItems.length) % groupedItems.length
                  ? activeDotClassName || "bg-[var(--primary)] shadow-lg"
                  : inactiveDotClassName || "bg-gray-400 hover:bg-gray-600"
              }`}
            />
          ))}
        </div>
      )}
    </div>
  );
}
